'use client';

import { useState, useEffect, useRef } from 'react';

interface ExamConfigProps {
  config: {
    examType: string;
    difficulty: string;
    passageType: string;
    includeAnswers: boolean;
    includeSummary: boolean;
  };
  onChange: (config: Partial<ExamConfigProps['config']>) => void;
  disabled?: boolean;
}

export function ExamConfig({ config, onChange, disabled = false }: ExamConfigProps) {
  // Helper type for keys of config
  type ConfigKeys = keyof ExamConfigProps['config'];
  const prevExamTypeRef = useRef(config.examType);
  
  const getIeltsDifficultyOptions = () => [
    { value: '5.0', label: 'Band 5.0 (Moderate)' },
    { value: '6.0', label: 'Band 6.0 (Competent)' },
    { value: '7.0', label: 'Band 7.0 (Good)' },
    { value: '8.0', label: 'Band 8.0 (Very Good)' },
    { value: '9.0', label: 'Band 9.0 (Expert)' },
  ];

  const getToeicDifficultyOptions = () => [
    { value: '400', label: '400-500 (Basic)' },
    { value: '550', label: '550-650 (Intermediate)' },
    { value: '700', label: '700-800 (Upper Intermediate)' },
    { value: '850', label: '850-900 (Advanced)' },
    { value: '950', label: '950+ (Proficient)' },
  ];

  const difficultyOptions = config.examType === 'ielts' 
    ? getIeltsDifficultyOptions() 
    : getToeicDifficultyOptions();

  // Reset difficulty value when exam type changes to ensure valid selection
  useEffect(() => {
    // Chỉ reset khi exam type thay đổi
    if (prevExamTypeRef.current !== config.examType) {
      onChange({ difficulty: '' });
      prevExamTypeRef.current = config.examType;
    }
  }, [config.examType]);

  return (
    <div className={`w-full p-6 bg-white rounded-xl shadow-xl transition-all duration-300 ease-in-out ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}>
      <div className="space-y-8">
        {/* Exam Type Section */}
        <div>
          <h3 className="text-lg font-semibold text-secondary-800 mb-3">Exam Type</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {[ { id: 'ielts', label: 'IELTS Reading' }, { id: 'toeic', label: 'TOEIC Reading' } ].map((item) => (
              <div key={item.id}>
                <input
                  type="radio"
                  id={item.id}
                  name="exam-type"
                  value={item.id}
                  checked={config.examType === item.id}
                  onChange={() => onChange({ examType: item.id })}
                  className="sr-only peer"
                  disabled={disabled}
                />
                <label
                  htmlFor={item.id}
                  className={`block w-full p-4 text-center rounded-lg border-2 transition-all duration-200 ease-in-out cursor-pointer 
                    ${disabled ? 'cursor-not-allowed bg-secondary-100 border-secondary-200' : 'peer-checked:border-primary-600 peer-checked:bg-primary-50 peer-checked:text-primary-700 peer-checked:shadow-md hover:border-primary-400 border-secondary-300 bg-white'}
                    ${config.examType === item.id ? 'border-primary-600 bg-primary-50 text-primary-700 shadow-md' : 'text-secondary-700'}`}
                >
                  <span className="text-sm font-medium">{item.label}</span>
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Difficulty Level Section */}
        <div>
          <label htmlFor="difficulty" className="block text-sm font-semibold text-secondary-700 mb-1">
            Difficulty Level
          </label>
          <div className="relative">
            <select
              id="difficulty"
              name="difficulty"
              value={config.difficulty}
              onChange={(e) => onChange({ difficulty: e.target.value })}
              className={`block w-full pl-3 pr-10 py-2.5 border border-secondary-300 rounded-lg leading-5 bg-white 
                text-secondary-900 placeholder-secondary-400 appearance-none 
                focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-colors duration-200 ease-in-out 
                ${disabled ? 'bg-secondary-100 cursor-not-allowed' : 'hover:border-secondary-400'}`}
              disabled={disabled}
            >
              <option value="" disabled className="text-secondary-400">
                Select difficulty...
              </option>
              {difficultyOptions.map((option) => (
                <option key={option.value} value={option.value} className="text-secondary-900">
                  {option.label}
                </option>
              ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-secondary-700">
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        {/* Passage Number Section */}
        <div>
          <h3 className="text-sm font-semibold text-secondary-700 mb-2">Number of Passages</h3>
          <div className="flex flex-wrap gap-3">
            {[ '1', '2', '3' ].map((num) => (
              <div key={`passage-${num}`}>
                <input
                  type="radio"
                  id={`passage-${num}`}
                  name="passage-type"
                  value={num}
                  checked={config.passageType === num}
                  onChange={() => onChange({ passageType: num })}
                  className="sr-only peer"
                  disabled={disabled}
                />
                <label
                  htmlFor={`passage-${num}`}
                  className={`px-5 py-2.5 text-sm font-medium text-center rounded-lg border transition-all duration-200 ease-in-out cursor-pointer 
                    ${disabled ? 'cursor-not-allowed bg-secondary-100 border-secondary-200' : 'peer-checked:border-primary-600 peer-checked:bg-primary-600 peer-checked:text-white peer-checked:shadow-md hover:border-primary-400 border-secondary-300 bg-white'}
                    ${config.passageType === num ? 'border-primary-600 bg-primary-600 text-white shadow-md' : 'text-secondary-700'}`}
                >
                  {num}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Options Section */}
        <div>
          <h3 className="text-sm font-semibold text-secondary-700 mb-2">Additional Options</h3>
          <div className="space-y-3">
            {[ { id: 'include-answers', label: 'Include answers and explanations', key: 'includeAnswers' }, 
               { id: 'include-summary', label: 'Include summary of the text', key: 'includeSummary' } ].map((item) => (
              <label key={item.id} htmlFor={item.id} className={`flex items-center p-3 rounded-lg border transition-all duration-200 ease-in-out cursor-pointer ${disabled ? 'cursor-not-allowed bg-secondary-100 border-secondary-200' : 'hover:border-primary-400 border-secondary-300 bg-white'} ${(config as any)[item.key] ? 'border-primary-600 bg-primary-50 shadow-sm' : ''}`}>
                <input
                  id={item.id}
                  name={item.id}
                  type="checkbox"
                  className={`h-5 w-5 rounded border-secondary-300 text-primary-600 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 ease-in-out ${disabled ? 'cursor-not-allowed' : ''}`}
                  checked={(config as any)[item.key]}
                  onChange={(e) => onChange({ [item.key]: e.target.checked })}
                  disabled={disabled}
                />
                <span className={`ml-3 text-sm font-medium ${(config as any)[item.key] ? 'text-primary-700' : 'text-secondary-700'}`}>
                  {item.label}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 